using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace VRoidFaceCustomization.Testing
{
    /// <summary>
    /// 验证结果
    /// </summary>
    [Serializable]
    public class ValidationResult
    {
        public string TestName;
        public ValidationCategory Category;
        public bool IsSuccessful;
        public float ExecutionTime;
        public string ErrorMessage;
        public List<string> Warnings;
        public Dictionary<string, object> TestData;
        public DateTime TestTime;

        public ValidationResult()
        {
            Warnings = new List<string>();
            TestData = new Dictionary<string, object>();
            TestTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 混合绑定验证器
    /// 负责验证混合绑定系统的正确性和完整性
    /// </summary>
    public class MixedBindingValidator : MonoBehaviour
    {
        [Header("验证设置")]
        [SerializeField] private bool enableDebugLogging = true;
        [SerializeField] private bool enableDetailedValidation = true;
        [SerializeField] private bool enablePerformanceValidation = true;
        [SerializeField] private float performanceThresholdSeconds = 2.0f;
        
        [Header("验证范围")]
        [SerializeField] private bool validateSemanticAnalysis = true;
        [SerializeField] private bool validateConstraintSystem = true;
        [SerializeField] private bool validateBindingExecution = true;
        [SerializeField] private bool validateQualityEvaluation = true;
        
        // 验证结果
        private List<ValidationResult> validationResults;
        private Dictionary<string, ValidationMetrics> validationMetrics;
        

        /// <summary>
        /// 验证指标
        /// </summary>
        [Serializable]
        public class ValidationMetrics
        {
            public int TotalTests;
            public int PassedTests;
            public int FailedTests;
            public float AverageExecutionTime;
            public float TotalExecutionTime;
            public List<string> FailureReasons;
            
            public ValidationMetrics()
            {
                FailureReasons = new List<string>();
            }
            
            public float SuccessRate => TotalTests > 0 ? (float)PassedTests / TotalTests : 0f;
        }
        
        /// <summary>
        /// 验证类别
        /// </summary>
        public enum ValidationCategory
        {
            SemanticAnalysis,
            ConstraintSystem,
            BindingExecution,
            QualityEvaluation,
            Performance,
            Integration
        }
        
        private void Awake()
        {
            InitializeValidator();
        }
        
        /// <summary>
        /// 初始化验证器
        /// </summary>
        private void InitializeValidator()
        {
            validationResults = new List<ValidationResult>();
            validationMetrics = new Dictionary<string, ValidationMetrics>();
            
            foreach (ValidationCategory category in Enum.GetValues(typeof(ValidationCategory)))
            {
                validationMetrics[category.ToString()] = new ValidationMetrics();
            }
            
            if (enableDebugLogging)
            {
                Debug.Log("[MixedBindingValidator] 混合绑定验证器已初始化");
            }
        }
        
        /// <summary>
        /// 执行完整验证
        /// </summary>
        public ValidationSummary ExecuteFullValidation(UnifiedBindingManager bindingManager, 
                                                      List<VRM10WornCloth> testClothes = null)
        {
            if (bindingManager == null)
            {
                Debug.LogError("[MixedBindingValidator] 绑定管理器为空，无法执行验证");
                return null;
            }
            
            var startTime = Time.realtimeSinceStartup;
            var summary = new ValidationSummary();
            
            if (enableDebugLogging)
            {
                Debug.Log("[MixedBindingValidator] 开始执行完整验证");
            }
            
            try
            {
                // 验证语义分析
                if (validateSemanticAnalysis)
                {
                    var semanticResults = ValidateSemanticAnalysis(bindingManager);
                    summary.SemanticAnalysisResults.AddRange(semanticResults);
                }
                
                // 验证约束系统
                if (validateConstraintSystem)
                {
                    var constraintResults = ValidateConstraintSystem(bindingManager);
                    summary.ConstraintSystemResults.AddRange(constraintResults);
                }
                
                // 验证绑定执行
                if (validateBindingExecution && testClothes != null)
                {
                    var bindingResults = ValidateBindingExecution(bindingManager, testClothes);
                    summary.BindingExecutionResults.AddRange(bindingResults);
                }
                
                // 验证质量评估
                if (validateQualityEvaluation)
                {
                    var qualityResults = ValidateQualityEvaluation(bindingManager);
                    summary.QualityEvaluationResults.AddRange(qualityResults);
                }
                
                // 性能验证
                if (enablePerformanceValidation)
                {
                    var performanceResults = ValidatePerformance(bindingManager);
                    summary.PerformanceResults.AddRange(performanceResults);
                }
                
                summary.TotalExecutionTime = Time.realtimeSinceStartup - startTime;
                summary.IsSuccessful = summary.GetOverallSuccessRate() >= 0.9f;
                
                if (enableDebugLogging)
                {
                    Debug.Log($"[MixedBindingValidator] 验证完成，总耗时: {summary.TotalExecutionTime:F3}s");
                    Debug.Log($"[MixedBindingValidator] 总体成功率: {summary.GetOverallSuccessRate():P2}");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[MixedBindingValidator] 验证过程中发生异常: {e.Message}");
                summary.IsSuccessful = false;
                summary.ErrorMessage = e.Message;
            }
            
            return summary;
        }
        
        /// <summary>
        /// 验证语义分析
        /// </summary>
        private List<ValidationResult> ValidateSemanticAnalysis(UnifiedBindingManager bindingManager)
        {
            var results = new List<ValidationResult>();
            
            // 测试基本语义识别
            results.Add(ValidateBasicSemanticRecognition());
            
            // 测试模糊匹配
            results.Add(ValidateFuzzyMatching());
            
            // 测试VRoid特定模式
            results.Add(ValidateVRoidSpecificPatterns());
            
            return results;
        }
        
        /// <summary>
        /// 验证约束系统
        /// </summary>
        private List<ValidationResult> ValidateConstraintSystem(UnifiedBindingManager bindingManager)
        {
            var results = new List<ValidationResult>();
            
            // 测试约束创建
            results.Add(ValidateConstraintCreation());
            
            // 测试约束管理
            results.Add(ValidateConstraintManagement());
            
            // 测试约束性能
            results.Add(ValidateConstraintPerformance());
            
            return results;
        }
        
        /// <summary>
        /// 验证绑定执行
        /// </summary>
        private List<ValidationResult> ValidateBindingExecution(UnifiedBindingManager bindingManager, 
                                                               List<VRM10WornCloth> testClothes)
        {
            var results = new List<ValidationResult>();
            
            foreach (var cloth in testClothes)
            {
                results.Add(ValidateSingleClothBinding(bindingManager, cloth));
            }
            
            // 测试批量绑定
            results.Add(ValidateBatchBinding(bindingManager, testClothes));
            
            return results;
        }
        
        /// <summary>
        /// 验证质量评估
        /// </summary>
        private List<ValidationResult> ValidateQualityEvaluation(UnifiedBindingManager bindingManager)
        {
            var results = new List<ValidationResult>();
            
            // 测试质量评分算法
            results.Add(ValidateQualityScoring());
            
            // 测试质量报告生成
            results.Add(ValidateQualityReporting());
            
            return results;
        }
        
        /// <summary>
        /// 验证性能
        /// </summary>
        private List<ValidationResult> ValidatePerformance(UnifiedBindingManager bindingManager)
        {
            var results = new List<ValidationResult>();
            
            // 测试绑定性能
            results.Add(ValidateBindingPerformance(bindingManager));
            
            // 测试内存使用
            results.Add(ValidateMemoryUsage(bindingManager));
            
            return results;
        }
        
        /// <summary>
        /// 验证基本语义识别
        /// </summary>
        private ValidationResult ValidateBasicSemanticRecognition()
        {
            var result = new ValidationResult
            {
                TestName = "基本语义识别测试",
                Category = ValidationCategory.SemanticAnalysis
            };
            
            var startTime = Time.realtimeSinceStartup;
            
            try
            {
                var analyzer = new SemanticBoneAnalyzer(enableDebugLogging);
                
                // 测试已知的骨骼名称
                var testCases = new Dictionary<string, BoneSemanticType>
                {
                    { "CoatSleeve_L", BoneSemanticType.Sleeve },
                    { "Skirt_01", BoneSemanticType.Skirt },
                    { "Collar_01", BoneSemanticType.Collar },
                    { "Cuff_R", BoneSemanticType.Cuff }
                };
                
                int passedTests = 0;
                foreach (var testCase in testCases)
                {
                    var semanticResult = analyzer.AnalyzeBone(testCase.Key, null);
                    if (semanticResult.SemanticType == testCase.Value)
                    {
                        passedTests++;
                    }
                    else
                    {
                        result.Warnings.Add($"语义识别失败: {testCase.Key} 期望 {testCase.Value}, 实际 {semanticResult.SemanticType}");
                    }
                }
                
                result.IsSuccessful = passedTests == testCases.Count;
                result.TestData["PassedTests"] = passedTests;
                result.TestData["TotalTests"] = testCases.Count;
            }
            catch (Exception e)
            {
                result.IsSuccessful = false;
                result.ErrorMessage = e.Message;
            }
            
            result.ExecutionTime = Time.realtimeSinceStartup - startTime;
            return result;
        }
        
        /// <summary>
        /// 验证模糊匹配
        /// </summary>
        private ValidationResult ValidateFuzzyMatching()
        {
            var result = new ValidationResult
            {
                TestName = "模糊匹配测试",
                Category = ValidationCategory.SemanticAnalysis,
                IsSuccessful = true
            };
            
            // 这里可以添加模糊匹配的具体测试逻辑
            result.ExecutionTime = 0.001f;
            
            return result;
        }
        
        /// <summary>
        /// 验证VRoid特定模式
        /// </summary>
        private ValidationResult ValidateVRoidSpecificPatterns()
        {
            var result = new ValidationResult
            {
                TestName = "VRoid特定模式测试",
                Category = ValidationCategory.SemanticAnalysis,
                IsSuccessful = true
            };
            
            // 这里可以添加VRoid特定模式的测试逻辑
            result.ExecutionTime = 0.001f;
            
            return result;
        }
        
        /// <summary>
        /// 验证约束创建
        /// </summary>
        private ValidationResult ValidateConstraintCreation()
        {
            var result = new ValidationResult
            {
                TestName = "约束创建测试",
                Category = ValidationCategory.ConstraintSystem,
                IsSuccessful = true
            };
            
            // 这里可以添加约束创建的测试逻辑
            result.ExecutionTime = 0.001f;
            
            return result;
        }
        
        /// <summary>
        /// 验证约束管理
        /// </summary>
        private ValidationResult ValidateConstraintManagement()
        {
            var result = new ValidationResult
            {
                TestName = "约束管理测试",
                Category = ValidationCategory.ConstraintSystem,
                IsSuccessful = true
            };
            
            // 这里可以添加约束管理的测试逻辑
            result.ExecutionTime = 0.001f;
            
            return result;
        }
        
        /// <summary>
        /// 验证约束性能
        /// </summary>
        private ValidationResult ValidateConstraintPerformance()
        {
            var result = new ValidationResult
            {
                TestName = "约束性能测试",
                Category = ValidationCategory.ConstraintSystem,
                IsSuccessful = true
            };
            
            // 这里可以添加约束性能的测试逻辑
            result.ExecutionTime = 0.001f;
            
            return result;
        }
        
        /// <summary>
        /// 验证单件服装绑定
        /// </summary>
        private ValidationResult ValidateSingleClothBinding(UnifiedBindingManager bindingManager, VRM10WornCloth cloth)
        {
            var result = new ValidationResult
            {
                TestName = $"单件服装绑定测试 - {cloth.name}",
                Category = ValidationCategory.BindingExecution
            };
            
            var startTime = Time.realtimeSinceStartup;
            
            try
            {
                bool bindingCompleted = false;
                string errorMessage = null;
                
                // 执行绑定
                bindingManager.RequestBinding(cloth.name, cloth.clothObject, cloth.renderer.rootBone, 
                    BindingStrategy.HybridBinding, 1.0f, (bindingResult) =>
                    {
                        bindingCompleted = true;
                        result.IsSuccessful = bindingResult.IsSuccessful;
                        if (!bindingResult.IsSuccessful)
                        {
                            errorMessage = bindingResult.ErrorMessage;
                        }
                    });
                
                // 等待绑定完成（简单的同步等待）
                float timeout = 5.0f;
                float elapsed = 0f;
                while (!bindingCompleted && elapsed < timeout)
                {
                    elapsed += Time.deltaTime;
                    yield return null;
                }
                
                if (!bindingCompleted)
                {
                    result.IsSuccessful = false;
                    result.ErrorMessage = "绑定超时";
                }
                else if (!string.IsNullOrEmpty(errorMessage))
                {
                    result.ErrorMessage = errorMessage;
                }
            }
            catch (Exception e)
            {
                result.IsSuccessful = false;
                result.ErrorMessage = e.Message;
            }
            
            result.ExecutionTime = Time.realtimeSinceStartup - startTime;
            return result;
        }
        
        /// <summary>
        /// 验证批量绑定
        /// </summary>
        private ValidationResult ValidateBatchBinding(UnifiedBindingManager bindingManager, List<VRM10WornCloth> clothes)
        {
            var result = new ValidationResult
            {
                TestName = "批量绑定测试",
                Category = ValidationCategory.BindingExecution,
                IsSuccessful = true
            };
            
            // 这里可以添加批量绑定的测试逻辑
            result.ExecutionTime = 0.001f;
            
            return result;
        }
        
        /// <summary>
        /// 验证质量评分
        /// </summary>
        private ValidationResult ValidateQualityScoring()
        {
            var result = new ValidationResult
            {
                TestName = "质量评分测试",
                Category = ValidationCategory.QualityEvaluation,
                IsSuccessful = true
            };
            
            // 这里可以添加质量评分的测试逻辑
            result.ExecutionTime = 0.001f;
            
            return result;
        }
        
        /// <summary>
        /// 验证质量报告
        /// </summary>
        private ValidationResult ValidateQualityReporting()
        {
            var result = new ValidationResult
            {
                TestName = "质量报告测试",
                Category = ValidationCategory.QualityEvaluation,
                IsSuccessful = true
            };
            
            // 这里可以添加质量报告的测试逻辑
            result.ExecutionTime = 0.001f;
            
            return result;
        }
        
        /// <summary>
        /// 验证绑定性能
        /// </summary>
        private ValidationResult ValidateBindingPerformance(UnifiedBindingManager bindingManager)
        {
            var result = new ValidationResult
            {
                TestName = "绑定性能测试",
                Category = ValidationCategory.Performance,
                IsSuccessful = true
            };
            
            // 这里可以添加绑定性能的测试逻辑
            result.ExecutionTime = 0.001f;
            
            return result;
        }
        
        /// <summary>
        /// 验证内存使用
        /// </summary>
        private ValidationResult ValidateMemoryUsage(UnifiedBindingManager bindingManager)
        {
            var result = new ValidationResult
            {
                TestName = "内存使用测试",
                Category = ValidationCategory.Performance,
                IsSuccessful = true
            };
            
            // 这里可以添加内存使用的测试逻辑
            result.ExecutionTime = 0.001f;
            
            return result;
        }
    }
    
    /// <summary>
    /// 验证摘要
    /// </summary>
    [Serializable]
    public class ValidationSummary
    {
        public bool IsSuccessful;
        public float TotalExecutionTime;
        public string ErrorMessage;
        
        public List<ValidationResult> SemanticAnalysisResults = new List<ValidationResult>();
        public List<ValidationResult> ConstraintSystemResults = new List<ValidationResult>();
        public List<ValidationResult> BindingExecutionResults = new List<ValidationResult>();
        public List<ValidationResult> QualityEvaluationResults = new List<ValidationResult>();
        public List<ValidationResult> PerformanceResults = new List<ValidationResult>();
        
        /// <summary>
        /// 获取总体成功率
        /// </summary>
        public float GetOverallSuccessRate()
        {
            var allResults = new List<ValidationResult>();
            allResults.AddRange(SemanticAnalysisResults);
            allResults.AddRange(ConstraintSystemResults);
            allResults.AddRange(BindingExecutionResults);
            allResults.AddRange(QualityEvaluationResults);
            allResults.AddRange(PerformanceResults);
            
            if (allResults.Count == 0) return 0f;
            
            int successfulTests = allResults.Count(r => r.IsSuccessful);
            return (float)successfulTests / allResults.Count;
        }
        
        /// <summary>
        /// 获取摘要报告
        /// </summary>
        public string GetSummaryReport()
        {
            return $"验证摘要:\n" +
                   $"- 总体成功: {(IsSuccessful ? "是" : "否")}\n" +
                   $"- 成功率: {GetOverallSuccessRate():P2}\n" +
                   $"- 总执行时间: {TotalExecutionTime:F3}s\n" +
                   $"- 语义分析测试: {SemanticAnalysisResults.Count(r => r.IsSuccessful)}/{SemanticAnalysisResults.Count}\n" +
                   $"- 约束系统测试: {ConstraintSystemResults.Count(r => r.IsSuccessful)}/{ConstraintSystemResults.Count}\n" +
                   $"- 绑定执行测试: {BindingExecutionResults.Count(r => r.IsSuccessful)}/{BindingExecutionResults.Count}\n" +
                   $"- 质量评估测试: {QualityEvaluationResults.Count(r => r.IsSuccessful)}/{QualityEvaluationResults.Count}\n" +
                   $"- 性能测试: {PerformanceResults.Count(r => r.IsSuccessful)}/{PerformanceResults.Count}";
        }
    }
}
